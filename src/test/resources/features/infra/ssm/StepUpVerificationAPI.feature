# language: en
Feature: Verify a Step-Up Session Factor
  As a client application
  I want to verify a specific factor for an active step-up session
  So that the session can proceed towards completion or be marked as failed.

  Scenario Outline: Successfully verify a single factor for an active step-up session
    Given A user profile exists in the system
    And The profile contains valid "<dataType>" information
    When I create a step-up session using Factor "<initialFactor>" authentication method with Profile Type: "PROFILE_ID" and config "<authConfig>"
    Then The step-up session should be successfully created
    When I validate the saved step-up session for factor <validationFactor> and request factor config <includeConfig>
    Then The validation response should be successful
    And The current factor for the session is <currentFactor> and is pending verification
    When I send an update request for the session with factor "<updateFactor>", status "<updateStatus>", attempt <attemptNumber>, and maxAttempts <maxAttempts>
    Then The service should respond with HTTP status 200
    When I verify the step-up session for factor <currentFactor> with verification data <verificationData>
    Then The verification response should be successful
    And The verification response should indicate overall status <expectedOverallStatus>
    And The underlying step-up session in the database should reflect factor <currentFactor> status as <expectedFactorStatus> and overall status as <expectedOverallStatus>

    Examples:
      | dataType | initialFactor | authConfig                                         | validationFactor | includeConfig | currentFactor | updateFactor | updateStatus | attemptNumber | maxAttempts | verificationData      | expectedOverallStatus | expectedFactorStatus |
      | phone    | OTP           | /dataShare/successCase/OTP_authConfig.json         | OTP              | true          | OTP           | OTP          | SUCCESS      | 1             | 3           | "otpCode:123456"      | FACTOR_SUCCESS        | SUCCESS              |
      | device   | DEVICE_BIO    | /dataShare/successCase/DEVICE_BIO_authConfig.json  | DEVICE_BIO       | true          | DEVICE_BIO    | DEVICE_BIO   | SUCCESS      | 1             | 3           | "bioChallenge:passed" | FACTOR_SUCCESS        | SUCCESS              |
      | device   | PASSCODE      | /dataShare/successCase/PASS_CODE_authConfig.json   | PASSCODE         | true          | PASSCODE      | PASSCODE     | SUCCESS      | 1             | 3           | "passcode:1234"       | FACTOR_SUCCESS        | SUCCESS              |
      | facial   | FACIAL        | /dataShare/successCase/FACIAL_authConfig.json      | FACIAL           | true          | FACIAL        | FACIAL       | SUCCESS      | 1             | 3           | "faceMatch:passed"    | FACTOR_SUCCESS        | SUCCESS              |

  Scenario Outline: Successfully verify multi-factor authentication session
    Given A user profile exists in the system
    And The profile contains valid "<dataType>" information
    When I create a step-up session using Factor "<initialFactors>" authentication method with Profile Type: "PROFILE_ID" and config "<authConfigs>"
    Then The step-up session should be successfully created
    When I validate the saved step-up session for factor <validationFactor> and request factor config <includeConfig>
    Then The validation response should be successful
    And The current factor for the session is <currentFactor> and is pending verification
    When I send an update request for the session with factor "<updateFactor>", status "<updateStatus>", attempt <attemptNumber>, and maxAttempts <maxAttempts>
    Then The service should respond with HTTP status 200
    When I verify the step-up session for factor <currentFactor> with verification data <verificationData>
    Then The verification response should be successful
    And The verification response should indicate overall status <expectedOverallStatus>
    And The underlying step-up session in the database should reflect factor <currentFactor> status as <expectedFactorStatus> and overall status as <expectedOverallStatus>
    And If a next factor <nextFactor> is expected, the verification response should indicate it with config matching <nextFactorConfigValidation>

    Examples:
      | dataType | initialFactors | authConfigs                                                    | validationFactor | includeConfig | currentFactor | updateFactor | updateStatus | attemptNumber | maxAttempts | verificationData      | expectedOverallStatus | expectedFactorStatus | nextFactor | nextFactorConfigValidation |
      | phone    | OTP            | /dataShare/successCase/OTP_authConfig.json                     | OTP              | true          | OTP           | OTP          | SUCCESS      | 1             | 3           | "otpCode:123456"      | VERIFICATION_SUCCESS  | SUCCESS              |            |                            |
      | device   | DEVICE_BIO     | /dataShare/successCase/DEVICE_BIO_authConfig.json              | DEVICE_BIO       | true          | DEVICE_BIO    | DEVICE_BIO   | SUCCESS      | 1             | 3           | "bioChallenge:passed" | VERIFICATION_SUCCESS  | SUCCESS              |            |                            |

  Scenario Outline: Verify step-up session with history trail included
    Given A user profile exists in the system
    And The profile contains valid "<dataType>" information
    When I create a step-up session using Factor "<initialFactor>" authentication method with Profile Type: "PROFILE_ID" and config "<authConfig>"
    Then The step-up session should be successfully created
    When I validate the saved step-up session for factor <validationFactor> and request factor config <includeConfig>
    Then The validation response should be successful
    And The current factor for the session is <currentFactor> and is pending verification
    When I send an update request for the session with factor "<updateFactor>", status "<updateStatus>", attempt <attemptNumber>, and maxAttempts <maxAttempts>
    Then The service should respond with HTTP status 200
    When I verify the step-up session for factor <currentFactor> with verification data <verificationData> including history trail
    Then The verification response should be successful
    And The verification response should indicate overall status <expectedOverallStatus>
    And The verification response should include history trail information

    Examples:
      | dataType | initialFactor | authConfig                                         | validationFactor | includeConfig | currentFactor | updateFactor | updateStatus | attemptNumber | maxAttempts | verificationData      | expectedOverallStatus |
      | phone    | OTP           | /dataShare/successCase/OTP_authConfig.json         | OTP              | true          | OTP           | OTP          | SUCCESS      | 1             | 3           | "otpCode:123456"      | VERIFICATION_SUCCESS  |
      | device   | DEVICE_BIO    | /dataShare/successCase/DEVICE_BIO_authConfig.json  | DEVICE_BIO       | true          | DEVICE_BIO    | DEVICE_BIO   | SUCCESS      | 1             | 3           | "bioChallenge:passed" | VERIFICATION_SUCCESS  |

  Scenario Outline: Attempt to verify step-up session with invalid flow ID
    Given A user profile exists in the system
    And The profile contains valid "phone" information
    When I create a step-up session using Factor "OTP" authentication method with Profile Type: "PROFILE_ID" and config "/dataShare/successCase/OTP_authConfig.json"
    Then The step-up session should be successfully created
    When I validate the saved step-up session for factor OTP and request factor config true
    Then The validation response should be successful
    And The current factor for the session is OTP and is pending verification
    When I send an update request for the session with factor "OTP", status "SUCCESS", attempt 1, and maxAttempts 3
    Then The service should respond with HTTP status 200
    When I verify the step-up session with invalid flow ID "<invalidFlowId>"
    Then The verification should fail with error code "<errorCode>" and message containing "<errorMessage>"

    Examples:
      | invalidFlowId    | errorCode           | errorMessage    |
      | WRONG_FLOW_ID    | FLOW_ID_NOT_MATCHED | flow id         |
      | INVALID_FLOW     | FLOW_ID_NOT_MATCHED | flow id         |

  Scenario: Attempt to verify non-existent step-up session
    Given A step-up session ID does not exist in the database
    When I verify the non-existent step-up session
    Then The verification should fail with error code "STEP_UP_SESSION_NOT_EXIST" and message containing "session"

  Scenario: Attempt to verify expired step-up session
    Given A user profile exists in the system
    And The profile contains valid "phone" information
    When I create a step-up session using Factor "OTP" authentication method with Profile Type: "PROFILE_ID" and config "/dataShare/successCase/OTP_authConfig.json"
    Then The step-up session should be successfully created
    When I validate the saved step-up session for factor OTP and request factor config true
    Then The validation response should be successful
    And The current factor for the session is OTP and is pending verification
    When I send an update request for the session with factor "OTP", status "SUCCESS", attempt 1, and maxAttempts 3
    Then The service should respond with HTTP status 200
    When The step-up session expires
    And I verify the expired step-up session
    Then The verification should fail with error code "STEP_UP_SESSION_EXPIRED" and message containing "expired"

  Scenario Outline: Attempt to verify step-up session with invalid request parameters
    Given A user profile exists in the system
    And The profile contains valid "phone" information
    When I create a step-up session using Factor "OTP" authentication method with Profile Type: "PROFILE_ID" and config "/dataShare/successCase/OTP_authConfig.json"
    Then The step-up session should be successfully created
    When I validate the saved step-up session for factor OTP and request factor config true
    Then The validation response should be successful
    And The current factor for the session is OTP and is pending verification
    When I send an update request for the session with factor "OTP", status "SUCCESS", attempt 1, and maxAttempts 3
    Then The service should respond with HTTP status 200
    When I verify the step-up session with invalid parameters: stepUpAuthId "<stepUpAuthId>", flowId "<flowId>"
    Then The verification should fail with validation error

    Examples:
      | stepUpAuthId | flowId      | description                    |
      |              | valid_flow  | Empty stepUpAuthId             |
      | valid_auth   |             | Empty flowId                   |
      |              |             | Both stepUpAuthId and flowId empty |

  Scenario: Verify step-up session with wrong factor status
    Given A user profile exists in the system
    And The profile contains valid "phone" information
    When I create a step-up session using Factor "OTP" authentication method with Profile Type: "PROFILE_ID" and config "/dataShare/successCase/OTP_authConfig.json"
    Then The step-up session should be successfully created
    When I validate the saved step-up session for factor OTP and request factor config true
    Then The validation response should be successful
    And The current factor for the session is OTP and is pending verification
    When I send an update request for the session with factor "OTP", status "FAILED", attempt 1, and maxAttempts 3
    Then The service should respond with HTTP status 200
    When I verify the step-up session for factor OTP with incorrect verification data "otpCode:wrong123"
    Then The verification should fail with error code "VERIFICATION_FAILED" and message containing "verification"

  Scenario: Verify completed step-up session
    Given A user profile exists in the system
    And The profile contains valid "phone" information
    When I create a step-up session using Factor "OTP" authentication method with Profile Type: "PROFILE_ID" and config "/dataShare/successCase/OTP_authConfig.json"
    Then The step-up session should be successfully created
    When I validate the saved step-up session for factor OTP and request factor config true
    Then The validation response should be successful
    And The current factor for the session is OTP and is pending verification
    When I send an update request for the session with factor "OTP", status "SUCCESS", attempt 1, and maxAttempts 3
    Then The service should respond with HTTP status 200
    When I verify the step-up session for factor OTP with verification data "otpCode:123456"
    Then The verification response should be successful
    When I attempt to verify the already completed step-up session again
    Then The verification should fail with error code "SESSION_ALREADY_COMPLETED" and message containing "completed"
