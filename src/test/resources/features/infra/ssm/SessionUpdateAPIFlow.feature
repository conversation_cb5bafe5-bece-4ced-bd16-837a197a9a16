# language: en
Feature: Updates of a Step-Up Session
  As a client application or internal factor service
  I want to update the result of an authentication attempt to a Step-Up Session
  So that the session state reflects the user's authentication journey.

  Scenario Outline: Successfully update a step-up session with various authentication factors
    Given A user profile exists in the system
    And The profile contains valid "<dataType>" information
    When I create a step-up session using Factor "<initialFactor>" authentication method with Profile Type: "PROFILE_ID" and config "<authConfigParams>"
    Then The step-up session should be successfully created
    When I send an update request for the session with factor "<updateFactor>", status "<updateStatus>", attempt <attemptNumber>, and maxAttempts <maxAttempts>
    Then The service should respond with HTTP status <statusCode>
    And The overall session status in the database should be "<expectedSessionStatus>"
    And The record for factor "<updateFactor>" should be updated with status "<updateStatus>" and attempt count <attemptNumber>
    And The session should have current factor "<expectedCurrentFactor>"

    Examples:
      | dataType | initialFactor | authConfigParams                                   | updateFactor | updateStatus | attemptNumber | maxAttempts | expectedSessionStatus | expectedCurrentFactor | statusCode |
      | phone    | OTP           | /dataShare/successCase/OTP_authConfig.json         | OTP          | SUCCESS      | 1             | 3           | FACTOR_SUCCESS        | null                  | 200        |
      | device   | DEVICE_BIO    | /dataShare/successCase/DEVICE_BIO_authConfig.json  | DEVICE_BIO   | SUCCESS      | 1             | 3           | FACTOR_SUCCESS        | null                  | 200        |
      | device   | PASSCODE      | /dataShare/successCase/PASS_CODE_authConfig.json   | PASSCODE     | SUCCESS      | 1             | 3           | FACTOR_SUCCESS        | null                  | 200        |
      | facial   | FACIAL        | /dataShare/successCase/FACIAL_authConfig.json      | FACIAL       | SUCCESS      | 1             | 3           | FACTOR_SUCCESS        | null                  | 200        |
      | phone    | OTP           | /dataShare/successCase/OTP_authConfig.json         | OTP          | FAILED       | 1             | 3           | FACTOR_FAILED         | null                  | 200        |
      | device   | DEVICE_BIO    | /dataShare/successCase/DEVICE_BIO_authConfig.json  | DEVICE_BIO   | FAILED       | 2             | 3           | FACTOR_FAILED         | null                  | 200        |

  Scenario Outline: Update a step-up session with different attempt numbers
    Given A user profile exists in the system
    And The profile contains valid "<dataType>" information
    When I create a step-up session using Factor "<initialFactor>" authentication method with Profile Type: "PROFILE_ID" and config "<authConfigParams>"
    Then The step-up session should be successfully created
    When I send an update request for the session with factor "<updateFactor>", status "<finalStatus>", attempt <attemptNumber>, and maxAttempts 3
    Then The service should respond with HTTP status 200
    And The overall session status in the database should be "<expectedSessionStatus>"
    And The record for factor "<updateFactor>" should be updated with status "<finalStatus>" and attempt count <attemptNumber>

    Examples:
      | dataType | initialFactor | authConfigParams                                   | updateFactor | finalStatus | attemptNumber | expectedSessionStatus |
      | phone    | OTP           | /dataShare/successCase/OTP_authConfig.json         | OTP          | SUCCESS     | 1             | FACTOR_SUCCESS        |
      | device   | DEVICE_BIO    | /dataShare/successCase/DEVICE_BIO_authConfig.json  | DEVICE_BIO   | SUCCESS     | 2             | FACTOR_SUCCESS        |
      | device   | PASSCODE      | /dataShare/successCase/PASS_CODE_authConfig.json   | PASSCODE     | FAILED      | 1             | FACTOR_FAILED         |

  Scenario Outline: Attempt to update a non-existent step-up session
    Given A step-up session ID does not exist in the database
    When I send an update request for non-existent session with factor "<factor>", status "<status>", attempt <attempt>, and maxAttempts <maxAttempts>
    Then The service should respond with HTTP status <statusCode>
    And The response should contain error code "<errorCode>"

    Examples:
      | factor     | status  | attempt | maxAttempts | statusCode | errorCode                 |
      | OTP        | SUCCESS | 1       | 3           | 400        | STEP_UP_SESSION_NOT_EXIST |
      | DEVICE_BIO | FAILED  | 1       | 3           | 400        | STEP_UP_SESSION_NOT_EXIST |

  Scenario: Attempt to update a session with invalid parameters
    Given A user profile exists in the system
    And The profile contains valid "phone" information
    When I create a step-up session using Factor "OTP" authentication method with Profile Type: "PROFILE_ID" and config "/dataShare/successCase/OTP_authConfig.json"
    Then The step-up session should be successfully created
    When I send an update request with invalid parameters
    Then The service should respond with HTTP status 400
    And The response should contain validation error





  Scenario Outline: Update session with fallback factor configuration
    Given A user profile exists in the system
    And The profile contains valid "<dataType>" information
    When I create a step-up session using Factor "<initialFactor>" authentication method with Profile Type: "PROFILE_ID" and config "<authConfigParams>"
    Then The step-up session should be successfully created
    When I send an update request for the session with factor "<updateFactor>", status "FAILED", attempt <maxAttempts>, and maxAttempts <maxAttempts>
    Then The service should respond with HTTP status 200
    And The overall session status in the database should be "<expectedSessionStatus>"
    And The session should have current factor "<expectedNextFactor>"

    Examples:
      | dataType | initialFactor | authConfigParams                                           | updateFactor | maxAttempts | expectedSessionStatus | expectedNextFactor |
      | phone    | OTP           | /dataShare/successCase/OTP_authConfig.json                 | OTP          | 3           | IN_PROGRESS           | PASSCODE           |
      | device   | DEVICE_BIO    | /dataShare/successCase/DEVICE_BIO_authConfig.json          | DEVICE_BIO   | 3           | FACTOR_FAILED         | null               |

  Scenario: Update session with keep current factor option
    Given A user profile exists in the system
    And The profile contains valid "phone" information
    When I create a step-up session using Factor "OTP" authentication method with Profile Type: "PROFILE_ID" and config "/dataShare/successCase/OTP_authConfig.json"
    Then The step-up session should be successfully created
    When I send an update request for the session with factor "OTP", status "FAILED", attempt 1, maxAttempts 3, and keepCurrentFactor true
    Then The service should respond with HTTP status 200
    And The overall session status in the database should be "IN_PROGRESS"
    And The session should have current factor "OTP"
