package com.tyme.tymex.stepupauth.cucumber;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.tyme.tymex.stepupauth.StepUpAuthApplicationTestsBase;
import com.tyme.tymex.stepupauth.controller.domain.InitStepUpSessionDto;
import com.tyme.tymex.stepupauth.controller.domain.StepUpSessionResponse;
import com.tyme.tymex.stepupauth.controller.domain.StepUpVerificationRequest;
import com.tyme.tymex.stepupauth.controller.domain.StepUpVerificationResponse;
import com.tyme.tymex.stepupauth.domain.*;
import com.tyme.tymex.stepupauth.infra.connector.model.DeviceIdType;
import com.tyme.tymex.stepupauth.infra.connector.model.profile.ProfilePhoneData;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.LinkDeviceInfo;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.StepUpAuthDeviceBioEnrollment;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.StepUpEntity;
import com.tyme.tymex.stepupauth.infra.exception.DomainException;
import com.tyme.tymex.stepupauth.repository.StepUpAuthDeviceBioEnrollmentRepo;
import com.tyme.tymex.stepupauth.repository.StepUpRepo;
import com.tyme.tymex.stepupauth.service.StepUpService;
import com.tyme.tymex.stepupauth.utils.GlobalUtils;
import io.cucumber.java.After;
import io.cucumber.java.Before;
import io.cucumber.java.ParameterType;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Assertions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Log4j2
@SpringBootTest
public class SessionVerifyAPICucumberSteps extends StepUpAuthApplicationTestsBase {


    @Autowired
    private StepUpService stepUpService;

    @Autowired
    private StepUpRepo stepUpRepo;

    @Autowired
    private StepUpAuthDeviceBioEnrollmentRepo deviceBioEnrollmentRepo;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable<LinkDeviceInfo> linkDeviceInfoTable;

    // State variables
    private String profileId;
    private String flowId;
    private String flowName;
    private String appId;
    private String savedStepUpAuthId;
    private StepUpSessionResponse initializedSessionResponse; // To store response from init
    private StepUpVerificationResponse verificationResponse;
    private Exception thrownVerificationException;
    @Autowired
    ScenarioContext scenarioContext;

    @ParameterType("OTP|FACIAL|DEVICE_BIO|PASSCODE")
    public AuthFactor authFactorTest(String value) {
        return AuthFactor.valueOf(value);
    }



    @Given("A step-up session has been initialized with factors {} for profile {} using configs {} and the auth ID and flow ID are saved")
    public void aStepUpSessionHasBeenInitializedWithFactorsForProfileUsingConfigsAndAuthIdFlowIdAreSaved(
            String factorsStr, String profileIdFromExample, String configsStr) {

        this.profileId = profileIdFromExample;
        String[] factorNames = factorsStr.split(";");
        List<InitStepUpSessionDto.AuthFactorRule> rules = new ArrayList<>();
        Map<AuthFactor, Object> authConfigMap = new HashMap<>();
        Map<String, String> allFactorConfigsRaw = Arrays.stream(configsStr.split(";"))
                .map(s -> s.split(":", 2))
                .filter(arr -> arr.length == 2)
                .collect(Collectors.toMap(arr -> arr[0].trim(), arr -> arr[1].trim(), (oldValue, newValue) -> newValue));
        for (String factorName : factorNames) {
            AuthFactor factor = AuthFactor.valueOf(factorName.trim());
            rules.add(InitStepUpSessionDto.AuthFactorRule.builder()
                    .factor(factor)
                    .build());
            if (allFactorConfigsRaw.containsKey(factor.name())) {
                setupAuthConfigForFactor(factor, authConfigMap, allFactorConfigsRaw.get(factor.name()));
            }
        }

        InitStepUpSessionDto initRequest = InitStepUpSessionDto.builder()
                .identifierId(this.profileId)
                .identifierType(IdentifierType.PROFILE_ID)
                .flowId(this.flowId)
                .flowName(this.flowName)
                .appId(this.appId)
                .authFactorRules(rules)
                .authConfig(authConfigMap)
                .expiredIn(600) // Default expiry
                .build();

        log.info("Initializing step-up session for profile: {}, factors: {}, configs: {}",
                this.profileId, factorsStr, configsStr);
        try {
            initializedSessionResponse = stepUpService.initStepUpSession(initRequest);
            Assertions.assertNotNull(initializedSessionResponse, "Initialized session response should not be null.");
            Assertions.assertNotNull(initializedSessionResponse.getStepUpAuthId(), "StepUpAuthId from init should not be null.");
            this.savedStepUpAuthId = initializedSessionResponse.getStepUpAuthId();
            log.info("Session initialized. AuthID: {}, FlowID: {}", savedStepUpAuthId, this.flowId);
        } catch (Exception e) {
            log.error("Failed to initialize step-up session for verification setup: {}", e.getMessage(), e);
            Assertions.fail("Prerequisite: Session initialization failed: " + e.getMessage());
        }
    }


    @Given("The current factor for the session is {} and is pending verification")
    public void theCurrentFactorForTheSessionIsAndIsPendingVerification(String expectedCurrentFactorStr) {
        savedStepUpAuthId = scenarioContext.getSessionResponse().getStepUpAuthId();
        flowId = scenarioContext.getFlowId();
        Assertions.assertNotNull(savedStepUpAuthId, "Step-up session must be initialized first.");
        StepUpEntity sessionEntity = stepUpRepo.findStepUpSessionByAuthId(savedStepUpAuthId);
        AuthFactor expectedCurrentFactor = AuthFactor.valueOf(expectedCurrentFactorStr);
        Assertions.assertEquals(expectedCurrentFactor, sessionEntity.getCurrentFactor(), "Current factor in DB does not match.");
        Assertions.assertTrue(
                sessionEntity.getOverallStatus() == StepUpStatus.IN_PROGRESS ||
                        sessionEntity.getOverallStatus() == StepUpStatus.FACTOR_FAILED,
                "Session status should be IN_PROGRESS or FACTOR_FAIL for verification."
        );
        log.info("Confirmed current factor is {} and session is pending verification for authId: {}", expectedCurrentFactor, savedStepUpAuthId);
    }

    // --- WHEN Steps ---

    @When("I verify the step-up session for factor {} with verification data {}")
    public void iVerifyTheStepUpSessionForFactorWithVerificationData(String factorStr, String verificationDataStr) {
        Assertions.assertNotNull(savedStepUpAuthId, "savedStepUpAuthId is null. Session not initialized properly.");
        Assertions.assertNotNull(flowId, "currentFlowId is null.");

        AuthFactor factor = AuthFactor.valueOf(factorStr);
        Map<String, Object> verificationDataMap = parseVerificationData(verificationDataStr);

        StepUpVerificationRequest request = StepUpVerificationRequest.builder()
                .stepUpAuthId(savedStepUpAuthId)
                .flowId(flowId)
                .build();

        log.info("Attempting to verify session. AuthID: {}, Factor: {}, Data: {}",
                savedStepUpAuthId, factor, verificationDataMap);
        try {
            verificationResponse = stepUpService.verifyStepUpSession(request);
            log.info("Verification call completed. Response: {}", verificationResponse);
        } catch (Exception e) {
            thrownVerificationException = e;
            log.error("Exception during verification: {}", e.getMessage(), e);
        }
    }

    // --- THEN Steps ---

    @Then("The verification response should be successful")
    public void theVerificationResponseShouldBeSuccessful() {
        Assertions.assertNull(thrownVerificationException,
                "Exception was thrown during verification: " +
                        (thrownVerificationException != null ? thrownVerificationException.getMessage() : "None"));
        Assertions.assertNotNull(verificationResponse, "Verification response should not be null.");
        log.info("the verification response is: {}",verificationResponse.toString());
        log.info("Verification response is successful.");
    }

    @Then("The verification response should indicate overall status {}")
    public void theVerificationResponseShouldIndicateOverallStatus(String expectedStatusStr) {
        Assertions.assertNotNull(verificationResponse, "Verification response is null.");
        StepUpStatus expectedStatus = StepUpStatus.valueOf(expectedStatusStr);
        Assertions.assertEquals(expectedStatus,StepUpStatus.valueOf("FACTOR_"+verificationResponse.getResult()), "status in response mismatch.");
        log.info("Verification response overall status: {}. Expected: {}", verificationResponse.getResult(), expectedStatus);
    }

    @Then("The underlying step-up session in the database should reflect factor {} status as {} and overall status as {}")
    public void theUnderlyingStepUpSessionInDbShouldReflectFactorStatusAndOverallStatus(String factorStr, String expectedFactorStatusStr, String expectedOverallStatusStr) {
        AuthFactor factor = AuthFactor.valueOf(factorStr);
        FactorStatus expectedFactorStatus = FactorStatus.valueOf(expectedFactorStatusStr);
        StepUpStatus expectedOverallStatus = StepUpStatus.valueOf(expectedOverallStatusStr);

        // Get session entity for overall status
        StepUpEntity sessionEntity = stepUpRepo.findStepUpSessionByAuthId(savedStepUpAuthId);
        Assertions.assertEquals(expectedOverallStatus, sessionEntity.getOverallStatus(), "Overall session status in DB mismatch.");

        // Get factor entity for factor status
        StepUpEntity factorEntity = stepUpRepo.queryByAuthId(savedStepUpAuthId)
                .filter(entity -> entity.getSk().equals(GlobalUtils.toFactorSk(factor)))
                .findFirst()
                .orElse(null);

        Assertions.assertNotNull(factorEntity, "Factor entity should exist in database");
        Assertions.assertEquals(expectedFactorStatus, factorEntity.getFactorResult(), "Factor status in DB mismatch.");

        log.info("DB state validated: Session overall status: {}, Factor {} status: {}",
                sessionEntity.getOverallStatus(), factor, factorEntity.getFactorResult());
    }

    @Then("If a next factor {} is expected, the verification response should indicate it with config matching {}")
    public void ifANextFactorIsExpectedTheResponseShouldIndicateItWithConfigMatching(String nextFactorStr, String expectedConfigValidation) {
        if (nextFactorStr != null && !nextFactorStr.isEmpty()) {
            AuthFactor expectedNextFactor = AuthFactor.valueOf(nextFactorStr);
            Assertions.assertNotNull(verificationResponse, "Verification response is null.");
            Assertions.assertEquals(expectedNextFactor, verificationResponse.getHistoryTrail().getFirst().getFactor(), "Next factor in response mismatch.");
            Assertions.assertNotNull(verificationResponse.getHistoryTrail().getLast().getFactor(), "Config for next factor should be present.");
            // Reuse verifyFactorConfig from SessionValidationAPICucumberSteps or a similar helper
            verifyFactorConfig(expectedNextFactor, expectedConfigValidation,verificationResponse.getHistoryTrail().getLast().getFactor());
            log.info("Next factor {} indicated with matching config {}.", expectedNextFactor, expectedConfigValidation);
        } else {
            log.info("No next factor expected, and none found in response.");
        }
    }

    @Then("The verification should fail with error code {} and message containing {}")
    public void theVerificationShouldFailWithErrorCodeAndMessageContaining(String expectedErrorCode, String expectedErrorMessagePart) {
        Assertions.assertNotNull(thrownVerificationException, "Expected an exception to be thrown for failed verification.");
        Assertions.assertInstanceOf(DomainException.class, thrownVerificationException, "Exception should be a DomainException.");
        DomainException ex = (DomainException) thrownVerificationException;
        Assertions.assertEquals(expectedErrorCode, ex.getErrorCode().toUniversalCode(), "Error code mismatch.");
        Assertions.assertTrue(ex.getMessage().contains(expectedErrorMessagePart),
                "Error message '" + ex.getMessage() + "' does not contain '" + expectedErrorMessagePart + "'.");
        log.info("Verification failed as expected with code {} and message containing '{}'", expectedErrorCode, expectedErrorMessagePart);
    }


    // --- Helper Methods (Adapted from SessionValidationAPICucumberSteps) ---

    private void setupDeviceBioTestData(String profileIdForDevice) {
        try {
            LinkDeviceInfo linkDeviceInfo = LinkDeviceInfo.builder()
                    .partitionKey(GlobalUtils.toLinkDeviceInfoPk(profileIdForDevice))
                    .sk(GlobalUtils.toLinkDeviceInfoSk(appId)) // Use currentAppId for consistency
                    .deviceId("ad4c48c67836ff97") // Standard test deviceId
                    .deviceIdType(DeviceIdType.AUTH_DEVICE_ID)
                    .deviceOs("iOS")
                    .linkedAt(Instant.now().minus(5, ChronoUnit.DAYS).toEpochMilli())
                    .build();
            linkDeviceInfoTable.putItem(linkDeviceInfo);

            StepUpAuthDeviceBioEnrollment enrollment = StepUpAuthDeviceBioEnrollment.builder()
                    .pk(GlobalUtils.toDeviceBioEnrollmentPk(profileIdForDevice))
                    .sk(GlobalUtils.getDeviceBioEnrollmentSk())
                    .deviceId("ad4c48c67836ff97")
                    .status(DeviceBioEnrollStatus.ENROLLED.name())
                    .eventTime(Instant.now().minus(2, ChronoUnit.DAYS).toEpochMilli())
                    .flowName(flowName) // Use currentFlowName
                    .build();
            deviceBioEnrollmentRepo.saveDeviceBioEnrollment(enrollment);
            log.info("Setup DEVICE_BIO test data for profileId: {}", profileIdForDevice);
        } catch (Exception e) {
            log.error("Failed to setup DEVICE_BIO test data for profile {}: {}", profileIdForDevice, e.getMessage(), e);
        }
    }

    private void setupAuthConfigForFactor(AuthFactor factor, Map<AuthFactor, Object> authConfigMap, String configParamsStr) {
        Map<String, String> params = parseConfigString(configParamsStr);
        // Logic adapted from SessionValidationAPICucumberSteps.setupAuthConfigForFactorWithParams
        switch (factor) {
            case OTP:
                OtpAuthConfig.OtpAuthConfigBuilder otpBuilder = OtpAuthConfig.builder();
                if (params.containsKey("dialCode")) otpBuilder.dialCode(params.get("dialCode"));
                if (params.containsKey("cellphone")) otpBuilder.cellphone(params.get("cellphone"));
                authConfigMap.put(AuthFactor.OTP, otpBuilder.build());
                break;
            case DEVICE_BIO:
                DeviceBioAuthConfig.DeviceBioAuthConfigBuilder dbBuilder = DeviceBioAuthConfig.builder();
                if (params.containsKey("deviceId")) dbBuilder.deviceId(params.get("deviceId"));
                if (params.containsKey("internalDeviceId")) dbBuilder.internalDeviceId(params.get("internalDeviceId"));
                if (params.containsKey("linkDeviceDays")) dbBuilder.linkDeviceDays(Long.parseLong(params.get("linkDeviceDays")));
                if (params.containsKey("deviceBioEnrollDays")) dbBuilder.deviceBioEnrollDays(Long.parseLong(params.get("deviceBioEnrollDays")));
                authConfigMap.put(AuthFactor.DEVICE_BIO, dbBuilder.build());
                break;
            case PASSCODE:
                PasscodeAuthConfig.PasscodeAuthConfigBuilder pcBuilder = PasscodeAuthConfig.builder();
                if (params.containsKey("deviceId")) pcBuilder.username("user-" + params.get("deviceId")); // Example mapping
                authConfigMap.put(AuthFactor.PASSCODE, pcBuilder.build());
                break;
            case FACIAL:
                Map<String, Object> facialConfig = new HashMap<>(params); // Simplified for example
                authConfigMap.put(AuthFactor.FACIAL, facialConfig);
                break;
            default:
                log.warn("No authConfig setup defined in helper for factor: {}", factor);
        }
    }

    private Map<String, String> parseConfigString(String configStr) {
        Map<String, String> params = new HashMap<>();
        if (configStr != null && !configStr.trim().isEmpty()) {
            for (String pair : configStr.split(",")) {
                String[] keyValue = pair.split(":", 2);
                if (keyValue.length == 2) {
                    params.put(keyValue[0].trim(), keyValue[1].trim());
                }
            }
        }
        return params;
    }

    private Map<String, Object> parseVerificationData(String verificationDataStr) {
        Map<String, Object> data = new HashMap<>();
        if (verificationDataStr != null && !verificationDataStr.trim().isEmpty()) {
            for (String pair : verificationDataStr.split(",")) {
                String[] keyValue = pair.split(":", 2);
                if (keyValue.length == 2) {
                    data.put(keyValue[0].trim(), keyValue[1].trim()); // Store as string, service layer can convert
                }
            }
        }
        return data;
    }


    private void verifyFactorConfig(AuthFactor factor, String configValidation, Object factorConfig) {
        // This method should be similar to the one in SessionValidationAPICucumberSteps
        // For brevity, I'm putting a placeholder. You should adapt the detailed assertions.
        log.info("Verifying config for factor {} against type '{}'. Actual config: {}", factor, configValidation, factorConfig);
        switch (factor) {
            case OTP:
                break;
            case DEVICE_BIO:
                if ("provided device data".equals(configValidation)) {
                    DeviceBioAuthConfig deviceBioConfig = objectMapper.convertValue(factorConfig, DeviceBioAuthConfig.class);
                    // Example: these values should match what was set in initConfig for DEVICE_BIO
                    // if the next factor was DEVICE_BIO and its config was "deviceId:devMulti,..."
                    Assertions.assertNotNull(deviceBioConfig.getDeviceId(), "Device ID for next factor should not be null");
                    // Add more specific assertions based on your 'initialConfigs' for the next factor
                }
                break;
            // Add cases for PASSCODE, FACIAL
            default:
                log.warn("No specific config validation in helper for factor: {} type: {}", factor, configValidation);
        }
        Assertions.assertNotNull(factorConfig, "Factor config for " + factor + " should not be null when expected.");
    }

    // ========================================
    // NEW STEP DEFINITIONS FOR ENHANCED TESTING
    // ========================================

    @When("I verify the step-up session for factor {} with verification data {} including history trail")
    public void iVerifyTheStepUpSessionWithHistoryTrail(String factorStr, String verificationDataStr) {
        Assertions.assertNotNull(savedStepUpAuthId, "savedStepUpAuthId is null. Session not initialized properly.");
        Assertions.assertNotNull(flowId, "currentFlowId is null.");

        AuthFactor factor = AuthFactor.valueOf(factorStr);
        Map<String, Object> verificationDataMap = parseVerificationData(verificationDataStr);

        StepUpVerificationRequest request = StepUpVerificationRequest.builder()
                .stepUpAuthId(savedStepUpAuthId)
                .flowId(flowId)
                .isIncludeHistoryTrail(true)
                .build();

        log.info("Attempting to verify session with history trail. AuthID: {}, Factor: {}, Data: {}",
                savedStepUpAuthId, factor, verificationDataMap);
        try {
            verificationResponse = stepUpService.verifyStepUpSession(request);
            log.info("Verification with history trail completed. Response: {}", verificationResponse);
        } catch (Exception e) {
            thrownVerificationException = e;
            log.error("Exception during verification with history trail: {}", e.getMessage(), e);
        }
    }

    @When("I verify the step-up session including history trail")
    public void iVerifyTheStepUpSessionIncludingHistoryTrail() {
        Assertions.assertNotNull(savedStepUpAuthId, "savedStepUpAuthId is null. Session not initialized properly.");
        Assertions.assertNotNull(flowId, "currentFlowId is null.");

        StepUpVerificationRequest request = StepUpVerificationRequest.builder()
                .stepUpAuthId(savedStepUpAuthId)
                .flowId(flowId)
                .isIncludeHistoryTrail(true)
                .build();

        log.info("Attempting to verify session with history trail. AuthID: {}", savedStepUpAuthId);
        try {
            verificationResponse = stepUpService.verifyStepUpSession(request);
            log.info("Verification with history trail completed. Response: {}", verificationResponse);
        } catch (Exception e) {
            thrownVerificationException = e;
            log.error("Exception during verification with history trail: {}", e.getMessage(), e);
        }
    }

    @Then("The verification response should include history trail information")
    public void theVerificationResponseShouldIncludeHistoryTrailInformation() {
        Assertions.assertNotNull(verificationResponse, "Verification response should not be null.");
        Assertions.assertNotNull(verificationResponse.getHistoryTrail(), "History trail should not be null when requested.");
        Assertions.assertFalse(verificationResponse.getHistoryTrail().isEmpty(), "History trail should not be empty.");
        log.info("Verification response includes history trail with {} entries", verificationResponse.getHistoryTrail().size());
    }

    @When("I verify the step-up session with invalid flow ID {string}")
    public void iVerifyTheStepUpSessionWithInvalidFlowId(String invalidFlowId) {
        Assertions.assertNotNull(savedStepUpAuthId, "savedStepUpAuthId is null. Session not initialized properly.");

        StepUpVerificationRequest request = StepUpVerificationRequest.builder()
                .stepUpAuthId(savedStepUpAuthId)
                .flowId(invalidFlowId)
                .build();

        log.info("Attempting to verify session with invalid flow ID: {}", invalidFlowId);
        try {
            verificationResponse = stepUpService.verifyStepUpSession(request);
            log.info("Verification with invalid flow ID completed unexpectedly. Response: {}", verificationResponse);
        } catch (Exception e) {
            thrownVerificationException = e;
            log.error("Exception during verification with invalid flow ID: {}", e.getMessage(), e);
        }
    }

    @When("I verify the non-existent step-up session")
    public void iVerifyTheNonExistentStepUpSession() {
        String nonExistentAuthId = "NON_EXISTENT_" + UUID.randomUUID();
        String nonExistentFlowId = "NON_EXISTENT_FLOW_" + UUID.randomUUID();

        StepUpVerificationRequest request = StepUpVerificationRequest.builder()
                .stepUpAuthId(nonExistentAuthId)
                .flowId(nonExistentFlowId)
                .build();

        log.info("Attempting to verify non-existent session: {}", nonExistentAuthId);
        try {
            verificationResponse = stepUpService.verifyStepUpSession(request);
            log.info("Verification of non-existent session completed unexpectedly. Response: {}", verificationResponse);
        } catch (Exception e) {
            thrownVerificationException = e;
            log.error("Exception during verification of non-existent session: {}", e.getMessage(), e);
        }
    }

    @When("The step-up session expires")
    public void theStepUpSessionExpires() {
        // Simulate session expiration by updating the database record
        StepUpEntity sessionEntity = stepUpRepo.findStepUpSessionByAuthId(savedStepUpAuthId);
        Assertions.assertNotNull(sessionEntity, "Session entity should exist");

        // Set expiry time to past and update modified date
        sessionEntity.setExpiredTime(Instant.now().minus(1, ChronoUnit.HOURS));
        sessionEntity.setModifiedDate(Instant.now());

        // Use transactUpsertWithRollback to update the entity
        // We need to create a dummy factor entity for the transaction requirement
        StepUpEntity dummyFactorEntity = StepUpEntity.builder()
                .pk(sessionEntity.getPk())
                .sk("DUMMY_FACTOR_FOR_UPDATE")
                .createdDate(Instant.now())
                .modifiedDate(Instant.now())
                .build();

        stepUpRepo.transactUpsertWithRollback(sessionEntity, dummyFactorEntity);

        log.info("Step-up session {} has been expired", savedStepUpAuthId);
    }

    @When("I verify the expired step-up session")
    public void iVerifyTheExpiredStepUpSession() {
        Assertions.assertNotNull(savedStepUpAuthId, "savedStepUpAuthId is null. Session not initialized properly.");
        Assertions.assertNotNull(flowId, "currentFlowId is null.");

        StepUpVerificationRequest request = StepUpVerificationRequest.builder()
                .stepUpAuthId(savedStepUpAuthId)
                .flowId(flowId)
                .build();

        log.info("Attempting to verify expired session: {}", savedStepUpAuthId);
        try {
            verificationResponse = stepUpService.verifyStepUpSession(request);
            log.info("Verification of expired session completed unexpectedly. Response: {}", verificationResponse);
        } catch (Exception e) {
            thrownVerificationException = e;
            log.error("Exception during verification of expired session: {}", e.getMessage(), e);
        }
    }

    @When("I verify the step-up session with invalid parameters: stepUpAuthId {string}, flowId {string}")
    public void iVerifyTheStepUpSessionWithInvalidParameters(String stepUpAuthId, String flowId) {
        // Handle empty string parameters
        String actualStepUpAuthId = stepUpAuthId.isEmpty() ? "" : (stepUpAuthId.equals("valid_auth") ? savedStepUpAuthId : stepUpAuthId);
        String actualFlowId = flowId.isEmpty() ? "" : (flowId.equals("valid_flow") ? this.flowId : flowId);

        StepUpVerificationRequest request = StepUpVerificationRequest.builder()
                .stepUpAuthId(actualStepUpAuthId)
                .flowId(actualFlowId)
                .build();

        log.info("Attempting to verify session with invalid parameters: stepUpAuthId='{}', flowId='{}'", actualStepUpAuthId, actualFlowId);
        try {
            verificationResponse = stepUpService.verifyStepUpSession(request);
            log.info("Verification with invalid parameters completed unexpectedly. Response: {}", verificationResponse);
        } catch (Exception e) {
            thrownVerificationException = e;
            log.error("Exception during verification with invalid parameters: {}", e.getMessage(), e);
        }
    }

    @Then("The verification should fail with validation error")
    public void theVerificationShouldFailWithValidationError() {
        Assertions.assertNotNull(thrownVerificationException, "Exception should be thrown for validation error");
        // Check for validation error indicators in exception message
        String errorMessage = thrownVerificationException.getMessage().toLowerCase();
        Assertions.assertTrue(errorMessage.contains("validation") || errorMessage.contains("invalid") ||
                errorMessage.contains("constraint") || errorMessage.contains("blank"),
                "Exception should contain validation error message");
        log.info("Verification failed with validation error: {}", thrownVerificationException.getMessage());
    }

    @When("I verify the step-up session for factor {} with incorrect verification data {string}")
    public void iVerifyTheStepUpSessionWithIncorrectVerificationData(String factorStr, String verificationDataStr) {
        Assertions.assertNotNull(savedStepUpAuthId, "savedStepUpAuthId is null. Session not initialized properly.");
        Assertions.assertNotNull(flowId, "currentFlowId is null.");

        AuthFactor factor = AuthFactor.valueOf(factorStr);
        Map<String, Object> verificationDataMap = parseVerificationData(verificationDataStr);

        StepUpVerificationRequest request = StepUpVerificationRequest.builder()
                .stepUpAuthId(savedStepUpAuthId)
                .flowId(flowId)
                .build();

        log.info("Attempting to verify session with incorrect data. AuthID: {}, Factor: {}, Data: {}",
                savedStepUpAuthId, factor, verificationDataMap);
        try {
            verificationResponse = stepUpService.verifyStepUpSession(request);
            log.info("Verification with incorrect data completed. Response: {}", verificationResponse);
        } catch (Exception e) {
            thrownVerificationException = e;
            log.error("Exception during verification with incorrect data: {}", e.getMessage(), e);
        }
    }

    @When("I attempt to verify the already completed step-up session again")
    public void iAttemptToVerifyTheAlreadyCompletedStepUpSessionAgain() {
        Assertions.assertNotNull(savedStepUpAuthId, "savedStepUpAuthId is null. Session not initialized properly.");
        Assertions.assertNotNull(flowId, "currentFlowId is null.");

        StepUpVerificationRequest request = StepUpVerificationRequest.builder()
                .stepUpAuthId(savedStepUpAuthId)
                .flowId(flowId)
                .build();

        log.info("Attempting to verify already completed session: {}", savedStepUpAuthId);
        try {
            verificationResponse = stepUpService.verifyStepUpSession(request);
            log.info("Verification of completed session completed unexpectedly. Response: {}", verificationResponse);
        } catch (Exception e) {
            thrownVerificationException = e;
            log.error("Exception during verification of completed session: {}", e.getMessage(), e);
        }
    }

}
